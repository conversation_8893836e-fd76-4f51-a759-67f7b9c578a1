import React from 'react';
import { useSelector } from 'react-redux';
import { BackgroundImage, Flex, Paragraph, Box, Text } from '@qga/roo-ui/components';
import { getCampaignBanner, getDealType, getFilters, getHeroImage, getNavigationItems, getRegionName } from 'store/deal/dealSelectors';
import { getIsLoading } from 'store/recommendedProperty/recommendedPropertySelectors';
import { DesktopCard, DealsHeaderWrapper, MobileCard, OptionsWrapper, DealsTitle } from './DealsHeader.style';
import NavigationMenu from './NavigationMenu';
import Filter from './Filter';
import PayWith from './PayWith';
import { getImage } from 'lib/sanity';
import CampaignBanner from './CampaignBanner';
import DealsHeaderSkeleton from './DealsHeaderSkeleton';
import { rem } from 'polished';
import { DEFAULT_DEAL_TYPE, PAYWITH_TOGGLE_ENABLED } from 'config';
import find from 'lodash/find';
import useCampaignMessagingVisibility from 'hooks/useCampaignMessagingVisibility';

const DealsHeader = () => {
  const navItems = useSelector(getNavigationItems);
  const dealType = useSelector(getDealType);
  const filters = useSelector(getFilters);
  const validDealType = () => find(filters.filters, ['code', dealType.code]) || DEFAULT_DEAL_TYPE;

  const regionName = useSelector(getRegionName);
  const campaignBanner = useSelector(getCampaignBanner);
  const heroImage = useSelector(getHeroImage);
  const heroImageSrc = getImage(heroImage?.asset);
  const heroImageHeight = campaignBanner ? [176, 176, 457] : [0, 0, 176, 288];
  const isLoading = useSelector(getIsLoading);
  const { name, slug, description } = validDealType() || {};

  // Check if global campaign messaging will be visible
  const isGlobalCampaignVisible = useCampaignMessagingVisibility();

  // Determine if any campaign banner is present (local or global)
  const hasAnyCampaignBanner = !!campaignBanner || isGlobalCampaignVisible;

  if (isLoading) return <DealsHeaderSkeleton />;
  return (
    <DealsHeaderWrapper>
      <Flex flexDirection={['column-reverse', 'column-reverse', 'column']}>
        {!!campaignBanner && <CampaignBanner {...campaignBanner} />}
        <BackgroundImage src={heroImageSrc?.large ?? ''} height={heroImageHeight} />
      </Flex>

      <OptionsWrapper
        hasCampaignBanner={hasAnyCampaignBanner}
        hasGlobalCampaign={isGlobalCampaignVisible}
        hasLocalCampaign={!!campaignBanner}
      >
        <DesktopCard>
          <Flex justifyContent="space-between" alignItems="space-between" flexDirection={['column', 'column', 'column', 'row']}>
            <MobileCard>
              <DealsTitle
                width="100%"
                data-testid="deals-header-heading"
                fontSize={[rem('28px'), 'xl']}
                display={['inline', '-webkit-inline-box']}
              >
                <Box display={['none', 'none', 'none', 'inline']}>{name} in&nbsp;</Box>
                <NavigationMenu items={navItems} dealType={slug} truncateText>
                  <DealsTitle as={Text} fontSize="28px" color="greys.charcoal" display={['inline', 'inline', 'inline', 'none']}>
                    {name} in&nbsp;
                  </DealsTitle>
                  {regionName}
                </NavigationMenu>
              </DealsTitle>
              <Paragraph display={['none', 'none', 'none', 'block']}>{description}</Paragraph>
              <Flex flexDirection={['column', 'column-reverse']} mt={[0, 0, 0, 4]}>
                <Paragraph mb={4} mt={[0, 6]} display={['inline', 'inline', 'none', 'inline']}>
                  These are the best deals available for the next 30 days. Prices are for 2 adults and 2 nights.
                </Paragraph>
                <Filter />
              </Flex>
              <Paragraph display={['none', 'none', 'inline', 'none']}>
                These are the best deals available for the next 30 days. Prices are for 2 adults and 2 nights.
              </Paragraph>
            </MobileCard>
            {PAYWITH_TOGGLE_ENABLED && (
              <Flex alignItems="center" mt={[0, 6, 0, 2]} pb={[0, 0, 0, 11]} data-testid="paywith-button">
                <PayWith name="payWith" />
              </Flex>
            )}
          </Flex>
        </DesktopCard>
      </OptionsWrapper>
    </DealsHeaderWrapper>
  );
};

export default DealsHeader;
