import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useSelector } from 'react-redux';
import * as dealSelectors from 'store/deal/dealSelectors';
import * as recommendedPropertySelectors from 'store/recommendedProperty/recommendedPropertySelectors';
import * as sanity from 'lib/sanity';
import useCampaignMessagingVisibility from 'hooks/useCampaignMessagingVisibility';
import DealsHeader from './DealsHeader';

const filterProps = (props) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { as, justifyContent, alignItems, flexDirection, ...filteredProps } = props;
  return filteredProps;
};

jest.mock('@qga/roo-ui/components', () => ({
  BackgroundImage: ({ children, src, height, ...props }) => (
    <div data-testid="mock-background-image" data-src={src} data-height={JSON.stringify(height)} {...filterProps(props)}>
      {children}
    </div>
  ),
  Flex: ({ children, ...props }) => (
    <div data-testid="mock-flex" {...filterProps(props)}>
      {children}
    </div>
  ),
  Paragraph: ({ children, ...props }) => <p {...filterProps(props)}>{children}</p>,
  Box: ({ children, ...props }) => (
    <div data-testid="mock-box" {...filterProps(props)}>
      {children}
    </div>
  ),
  Text: ({ children, ...props }) => (
    <span data-testid="mock-text" {...filterProps(props)}>
      {children}
    </span>
  ),
  Heading: {
    h1: ({ children, ...props }) => <h1 {...filterProps(props)}>{children}</h1>,
  },
}));

jest.mock('./DealsHeader.style', () => ({
  DesktopCard: ({ children, ...props }) => (
    <div data-testid="mock-desktop-card" {...filterProps(props)}>
      {children}
    </div>
  ),
  DealsHeaderWrapper: ({ children, ...props }) => (
    <div data-testid="mock-deals-header-wrapper" {...filterProps(props)}>
      {children}
    </div>
  ),
  MobileCard: ({ children, ...props }) => (
    <div data-testid="mock-mobile-card" {...filterProps(props)}>
      {children}
    </div>
  ),
  OptionsWrapper: ({ children, hasCampaignBanner, hasGlobalCampaign, hasLocalCampaign, ...props }) => (
    <div
      data-testid="mock-options-wrapper"
      data-has-campaign-banner={hasCampaignBanner}
      data-has-global-campaign={hasGlobalCampaign}
      data-has-local-campaign={hasLocalCampaign}
      {...filterProps(props)}
    >
      {children}
    </div>
  ),
  DealsTitle: ({ children, ...props }) => (
    <div data-testid="mock-deals-title" {...filterProps(props)}>
      {children}
    </div>
  ),
}));

jest.mock('./NavigationMenu', () => ({ items, dealType, truncateText, children }) => (
  <nav data-testid="mock-navigation-menu" data-items={JSON.stringify(items)} data-deal-type={dealType} data-truncate-text={truncateText}>
    {children}
  </nav>
));
jest.mock('./Filter', () => () => <div data-testid="mock-filter" />);
jest.mock('./PayWith', () => () => <div data-testid="mock-paywith" />);
jest.mock('./CampaignBanner', () => (props) => <div data-testid="mock-campaign-banner" data-props={JSON.stringify(props)} />);
jest.mock('./DealsHeaderSkeleton', () => () => <div data-testid="mock-deals-header-skeleton" />);

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('lib/sanity', () => ({
  getImage: jest.fn((asset) => {
    if (asset && asset._ref === 'image-ref-123') {
      return { large: '/mock-hero-image-large.jpg' };
    }
    return null;
  }),
}));

jest.mock('config', () => ({
  DEFAULT_DEAL_TYPE: { code: 'flights', name: 'Flights', slug: 'flights', description: 'Flight deals' },
  PAYWITH_TOGGLE_ENABLED: true,
}));

jest.mock('hooks/useCampaignMessagingVisibility', () => jest.fn(() => false));

describe('DealsHeader', () => {
  const mockDealType = { code: 'flights', name: 'Flights', slug: 'flights', description: 'Find flights' };
  const mockFilters = {
    filters: [
      { code: 'flights', name: 'Flights', slug: 'flights', description: 'Flight deals' },
      { code: 'hotels', name: 'Hotels', slug: 'hotels', description: 'Hotel deals' },
    ],
  };
  const mockNavigationItems = [{ id: 'nav1', name: 'Nav Item 1' }];
  const mockRegionName = 'Sydney';
  const mockHeroImage = { asset: { _ref: 'image-ref-123' } };
  const mockCampaignBanner = { title: 'Campaign Deal', text: 'Special offer!' };
  const mockUseSelector = useSelector as jest.Mock;
  const mockGetImage = sanity.getImage as jest.Mock;

  beforeEach(() => {
    mockUseSelector.mockClear();
    mockGetImage.mockClear();

    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getDealType) return mockDealType;
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getCampaignBanner) return null;
      if (selector === dealSelectors.getHeroImage) return mockHeroImage;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });
  });

  it('should render the DealsHeader with default content when not loading and no campaign banner', () => {
    render(<DealsHeader />);

    expect(screen.getByTestId('mock-deals-header-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('mock-options-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('mock-desktop-card')).toBeInTheDocument();
    expect(screen.getByTestId('mock-mobile-card')).toBeInTheDocument();

    expect(screen.getByTestId('deals-header-heading')).toHaveTextContent('Flights in Sydney');

    expect(screen.getAllByText(/These are the best deals available for the next 30 days/i)).toHaveLength(2);

    expect(screen.getByTestId('mock-navigation-menu')).toBeInTheDocument();
    expect(screen.getByTestId('mock-filter')).toBeInTheDocument();
    expect(screen.getByTestId('mock-paywith')).toBeInTheDocument();
    expect(screen.getByTestId('paywith-button')).toBeInTheDocument();

    expect(screen.getByTestId('mock-background-image')).toHaveAttribute('data-src', '/mock-hero-image-large.jpg');
    expect(sanity.getImage).toHaveBeenCalledWith(mockHeroImage.asset);

    expect(screen.queryByTestId('mock-campaign-banner')).not.toBeInTheDocument();
    expect(screen.getByTestId('mock-options-wrapper')).toHaveAttribute('data-has-campaign-banner', 'false');
  });

  it('should render DealsHeaderSkeleton when isLoading is true', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === recommendedPropertySelectors.getIsLoading) return true;
      return jest.fn();
    });
    render(<DealsHeader />);

    expect(screen.getByTestId('mock-deals-header-skeleton')).toBeInTheDocument();
    expect(screen.queryByTestId('deals-header-heading')).not.toBeInTheDocument();
  });

  it('should render CampaignBanner when campaignBanner is present', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getCampaignBanner) return mockCampaignBanner;
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getDealType) return mockDealType;
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getHeroImage) return mockHeroImage;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });
    render(<DealsHeader />);

    const campaignBannerComponent = screen.getByTestId('mock-campaign-banner');
    expect(campaignBannerComponent).toBeInTheDocument();
    expect(campaignBannerComponent).toHaveAttribute('data-props', JSON.stringify(mockCampaignBanner));

    expect(screen.getByTestId('mock-background-image')).toHaveAttribute('data-height', '[176,176,457]');
    expect(screen.getByTestId('mock-options-wrapper')).toHaveAttribute('data-has-campaign-banner', 'true');
  });

  it('should not render PayWith component when PAYWITH_TOGGLE_ENABLED is false', () => {
    jest.resetModules();

    jest.doMock('config', () => ({
      ...jest.requireActual('config'),
      PAYWITH_TOGGLE_ENABLED: false,
    }));

    // require imports here otherwise jest fails
    /* eslint-disable @typescript-eslint/no-var-requires */
    const DealsHeaderReimported = require('./DealsHeader').default;
    const redux = require('react-redux');
    const dealSel = require('store/deal/dealSelectors');
    const recommendedSel = require('store/recommendedProperty/recommendedPropertySelectors');
    const sanityReimported = require('lib/sanity');
    /* eslint-enable @typescript-eslint/no-var-requires */

    redux.useSelector.mockImplementation((selector) => {
      if (selector === dealSel.getNavigationItems) return mockNavigationItems;
      if (selector === dealSel.getDealType) return mockDealType;
      if (selector === dealSel.getFilters) return mockFilters;
      if (selector === dealSel.getRegionName) return mockRegionName;
      if (selector === dealSel.getCampaignBanner) return null;
      if (selector === dealSel.getHeroImage) return mockHeroImage;
      if (selector === recommendedSel.getIsLoading) return false;
      return undefined;
    });
    sanityReimported.getImage.mockImplementation((asset) => {
      if (asset && asset._ref === 'image-ref-123') {
        return { large: '/mock-hero-image-large.jpg' };
      }
      return null;
    });

    render(<DealsHeaderReimported />);

    expect(screen.queryByTestId('mock-paywith')).not.toBeInTheDocument();
    expect(screen.queryByTestId('paywith-button')).not.toBeInTheDocument();
  });

  it('should use DEFAULT_DEAL_TYPE if dealType from selector is not found in filters', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getDealType)
        return { code: 'nonexistent', name: 'Nonexistent', slug: 'nonexistent', description: 'Nonexistent deal' };
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getCampaignBanner) return null;
      if (selector === dealSelectors.getHeroImage) return mockHeroImage;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });

    render(<DealsHeader />);

    expect(screen.getByTestId('deals-header-heading')).toHaveTextContent('Flights in Sydney');
    expect(screen.getByText(/Flight deals/i)).toBeInTheDocument();
  });

  it('should handle missing heroImage gracefully and display empty src', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getHeroImage) return null;
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getDealType) return mockDealType;
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getCampaignBanner) return null;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });
    mockGetImage.mockReturnValue(null);

    render(<DealsHeader />);

    expect(screen.getByTestId('mock-background-image')).toHaveAttribute('data-src', '');
  });

  it('should pass correct props to NavigationMenu', () => {
    render(<DealsHeader />);
    const navigationMenu = screen.getByTestId('mock-navigation-menu');
    expect(navigationMenu).toHaveAttribute('data-items', JSON.stringify(mockNavigationItems));
    expect(navigationMenu).toHaveAttribute('data-deal-type', mockDealType.slug);
    expect(navigationMenu).toHaveAttribute('data-truncate-text', 'true');
  });

  it('should render correct hero image height without campaign banner', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getCampaignBanner) return null;
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getDealType) return mockDealType;
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getHeroImage) return mockHeroImage;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });
    render(<DealsHeader />);
    expect(screen.getByTestId('mock-background-image')).toHaveAttribute('data-height', '[0,0,176,288]');
  });

  it('should render correct hero image height with campaign banner', () => {
    mockUseSelector.mockImplementation((selector) => {
      if (selector === dealSelectors.getCampaignBanner) return mockCampaignBanner;
      if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
      if (selector === dealSelectors.getDealType) return mockDealType;
      if (selector === dealSelectors.getFilters) return mockFilters;
      if (selector === dealSelectors.getRegionName) return mockRegionName;
      if (selector === dealSelectors.getHeroImage) return mockHeroImage;
      if (selector === recommendedPropertySelectors.getIsLoading) return false;
      return undefined;
    });
    render(<DealsHeader />);
    expect(screen.getByTestId('mock-background-image')).toHaveAttribute('data-height', '[176,176,457]');
  });

  describe('Campaign Banner Positioning', () => {
    it('should pass correct props when only global campaign is active', () => {
      const mockUseCampaignMessagingVisibility = useCampaignMessagingVisibility as jest.Mock;
      mockUseCampaignMessagingVisibility.mockReturnValue(true);

      mockUseSelector.mockImplementation((selector) => {
        if (selector === dealSelectors.getCampaignBanner) return null; // No local campaign
        if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
        if (selector === dealSelectors.getDealType) return mockDealType;
        if (selector === dealSelectors.getFilters) return mockFilters;
        if (selector === dealSelectors.getRegionName) return mockRegionName;
        if (selector === dealSelectors.getHeroImage) return mockHeroImage;
        if (selector === recommendedPropertySelectors.getIsLoading) return false;
        return undefined;
      });

      render(<DealsHeader />);

      const optionsWrapper = screen.getByTestId('mock-options-wrapper');
      expect(optionsWrapper).toHaveAttribute('data-has-campaign-banner', 'true'); // Any campaign
      expect(optionsWrapper).toHaveAttribute('data-has-global-campaign', 'true');
      expect(optionsWrapper).toHaveAttribute('data-has-local-campaign', 'false');
    });

    it('should pass correct props when only local campaign is active', () => {
      const mockUseCampaignMessagingVisibility = useCampaignMessagingVisibility as jest.Mock;
      mockUseCampaignMessagingVisibility.mockReturnValue(false);

      mockUseSelector.mockImplementation((selector) => {
        if (selector === dealSelectors.getCampaignBanner) return mockCampaignBanner; // Local campaign active
        if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
        if (selector === dealSelectors.getDealType) return mockDealType;
        if (selector === dealSelectors.getFilters) return mockFilters;
        if (selector === dealSelectors.getRegionName) return mockRegionName;
        if (selector === dealSelectors.getHeroImage) return mockHeroImage;
        if (selector === recommendedPropertySelectors.getIsLoading) return false;
        return undefined;
      });

      render(<DealsHeader />);

      const optionsWrapper = screen.getByTestId('mock-options-wrapper');
      expect(optionsWrapper).toHaveAttribute('data-has-campaign-banner', 'true'); // Any campaign
      expect(optionsWrapper).toHaveAttribute('data-has-global-campaign', 'false');
      expect(optionsWrapper).toHaveAttribute('data-has-local-campaign', 'true');
    });

    it('should pass correct props when both campaigns are active', () => {
      const mockUseCampaignMessagingVisibility = useCampaignMessagingVisibility as jest.Mock;
      mockUseCampaignMessagingVisibility.mockReturnValue(true);

      mockUseSelector.mockImplementation((selector) => {
        if (selector === dealSelectors.getCampaignBanner) return mockCampaignBanner; // Local campaign active
        if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
        if (selector === dealSelectors.getDealType) return mockDealType;
        if (selector === dealSelectors.getFilters) return mockFilters;
        if (selector === dealSelectors.getRegionName) return mockRegionName;
        if (selector === dealSelectors.getHeroImage) return mockHeroImage;
        if (selector === recommendedPropertySelectors.getIsLoading) return false;
        return undefined;
      });

      render(<DealsHeader />);

      const optionsWrapper = screen.getByTestId('mock-options-wrapper');
      expect(optionsWrapper).toHaveAttribute('data-has-campaign-banner', 'true'); // Any campaign
      expect(optionsWrapper).toHaveAttribute('data-has-global-campaign', 'true');
      expect(optionsWrapper).toHaveAttribute('data-has-local-campaign', 'true');
    });

    it('should pass correct props when no campaigns are active', () => {
      const mockUseCampaignMessagingVisibility = useCampaignMessagingVisibility as jest.Mock;
      mockUseCampaignMessagingVisibility.mockReturnValue(false);

      mockUseSelector.mockImplementation((selector) => {
        if (selector === dealSelectors.getCampaignBanner) return null; // No local campaign
        if (selector === dealSelectors.getNavigationItems) return mockNavigationItems;
        if (selector === dealSelectors.getDealType) return mockDealType;
        if (selector === dealSelectors.getFilters) return mockFilters;
        if (selector === dealSelectors.getRegionName) return mockRegionName;
        if (selector === dealSelectors.getHeroImage) return mockHeroImage;
        if (selector === recommendedPropertySelectors.getIsLoading) return false;
        return undefined;
      });

      render(<DealsHeader />);

      const optionsWrapper = screen.getByTestId('mock-options-wrapper');
      expect(optionsWrapper).toHaveAttribute('data-has-campaign-banner', 'false'); // No campaigns
      expect(optionsWrapper).toHaveAttribute('data-has-global-campaign', 'false');
      expect(optionsWrapper).toHaveAttribute('data-has-local-campaign', 'false');
    });
  });
});
