import { useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { getCampaignTitle, getCampaignMessage, getCampaignIsEnabled } from 'store/campaign/campaignSelectors';
import { getIsExclusive } from 'store/property/propertySelectors';
import { CAMPAIGN_BANNER_ENABLED } from 'config';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';

/**
 * Custom hook that determines if the CampaignMessaging component will be visible.
 * This replicates the visibility logic from CampaignMessaging component to allow
 * other components (like DealsHeader) to adjust their layout accordingly.
 *
 * @returns boolean indicating if the global campaign messaging banner will be displayed
 */
const useCampaignMessagingVisibility = (): boolean => {
  const globalCampaignTitle = useSelector(getCampaignTitle);
  const globalCampaignMessage = useSelector(getCampaignMessage);
  const globalCampaignEnabled = useSelector(getCampaignIsEnabled);
  const { isStandardCampaignEnabled, message: standardCampaignMessage, title: standardCampaignTitle } = useStandardCampaign();
  const { isPointsRedemptionStrikethrough, campaignMessage: pointsRedemptionStrikethroughMessage } = usePointsRedemptionStrikethrough();
  const isExclusive = useSelector(getIsExclusive);

  // Determine which campaign content to show (same logic as CampaignMessaging)
  let title: string | null;
  let message: string | null;

  if (globalCampaignEnabled) {
    title = globalCampaignTitle ?? null;
    message = globalCampaignMessage ?? null;
  } else if (isPointsRedemptionStrikethrough) {
    title = pointsRedemptionStrikethroughMessage ?? null;
    message = null;
  } else if (isStandardCampaignEnabled) {
    title = standardCampaignTitle ?? null;
    message = standardCampaignMessage ?? null;
  } else {
    title = null;
    message = null;
  }

  // Check visibility conditions (same logic as CampaignMessaging)
  const noContent = isEmpty(title) && isEmpty(message);
  const noActiveCampaigns = !globalCampaignEnabled && !isStandardCampaignEnabled && !isPointsRedemptionStrikethrough;

  // Return false if any condition prevents rendering
  if (!CAMPAIGN_BANNER_ENABLED || isExclusive || noActiveCampaigns || noContent) {
    return false;
  }

  return true;
};

export default useCampaignMessagingVisibility;
