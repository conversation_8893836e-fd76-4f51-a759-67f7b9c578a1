import { renderHook } from '@testing-library/react-hooks';
import { useSelector } from 'react-redux';
import useCampaignMessagingVisibility from './useCampaignMessagingVisibility';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';
import usePointsRedemptionStrikethrough from 'hooks/optimizely/usePointsRedemptionStrikethrough';

jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
}));

jest.mock('hooks/optimizely/useStandardCampaign');
jest.mock('hooks/optimizely/usePointsRedemptionStrikethrough');

jest.mock('config', () => ({
  CAMPAIGN_BANNER_ENABLED: true,
}));

const mockUseSelector = useSelector as jest.Mock;
const mockUseStandardCampaign = useStandardCampaign as jest.Mock;
const mockUsePointsRedemptionStrikethrough = usePointsRedemptionStrikethrough as jest.Mock;

describe('useCampaignMessagingVisibility', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseSelector.mockImplementation(() => {
      return null;
    });

    mockUseStandardCampaign.mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: false,
      message: '',
      title: '',
    });

    mockUsePointsRedemptionStrikethrough.mockReturnValue({
      isReady: true,
      isPointsRedemptionStrikethrough: false,
      tooltipMessage: '',
      campaignMessage: '',
      disclaimer: '',
    });
  });

  it('returns false when no campaigns are active', () => {
    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(false);
  });

  it('returns true when global campaign is enabled with content', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return 'Global Campaign Title'; // getCampaignTitle
      if (callCount === 2) return 'Global Campaign Message'; // getCampaignMessage
      if (callCount === 3) return true; // getCampaignIsEnabled
      if (callCount === 4) return false; // getIsExclusive
      return null;
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(true);
  });

  it('returns false when global campaign is enabled but has no content', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return ''; // getCampaignTitle - empty
      if (callCount === 2) return ''; // getCampaignMessage - empty
      if (callCount === 3) return true; // getCampaignIsEnabled
      if (callCount === 4) return false; // getIsExclusive
      return null;
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(false);
  });

  it('returns true when standard campaign is enabled with content', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return null; // getCampaignTitle
      if (callCount === 2) return null; // getCampaignMessage
      if (callCount === 3) return false; // getCampaignIsEnabled - disabled
      if (callCount === 4) return false; // getIsExclusive
      return null;
    });

    mockUseStandardCampaign.mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: true,
      message: 'Standard Campaign Message',
      title: 'Standard Campaign Title',
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(true);
  });

  it('returns true when points redemption strikethrough is enabled with content', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return null; // getCampaignTitle
      if (callCount === 2) return null; // getCampaignMessage
      if (callCount === 3) return false; // getCampaignIsEnabled - disabled
      if (callCount === 4) return false; // getIsExclusive
      return null;
    });

    mockUsePointsRedemptionStrikethrough.mockReturnValue({
      isReady: true,
      isPointsRedemptionStrikethrough: true,
      tooltipMessage: '',
      campaignMessage: 'Points Redemption Message',
      disclaimer: '',
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(true);
  });

  it('returns false when property is exclusive', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return 'Global Campaign Title'; // getCampaignTitle
      if (callCount === 2) return 'Global Campaign Message'; // getCampaignMessage
      if (callCount === 3) return true; // getCampaignIsEnabled
      if (callCount === 4) return true; // getIsExclusive - exclusive property
      return null;
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(false);
  });

  it('prioritizes global campaign over standard campaign', () => {
    let callCount = 0;
    mockUseSelector.mockImplementation(() => {
      callCount++;
      if (callCount === 1) return 'Global Campaign Title'; // getCampaignTitle
      if (callCount === 2) return 'Global Campaign Message'; // getCampaignMessage
      if (callCount === 3) return true; // getCampaignIsEnabled
      if (callCount === 4) return false; // getIsExclusive
      return null;
    });

    mockUseStandardCampaign.mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: true,
      message: 'Standard Campaign Message',
      title: 'Standard Campaign Title',
    });

    const { result } = renderHook(() => useCampaignMessagingVisibility());
    expect(result.current).toBe(true);
  });
});
